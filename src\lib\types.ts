import { User<PERSON><PERSON>, CampaignStatus, AdFormat, PricingModel } from "@prisma/client"

export interface User {
  id: string
  name?: string
  email: string
  role?: UserRole
  isOnboarded?: boolean
  createdAt: Date
  updatedAt: Date
}

export interface PublisherProfile {
  id: string
  userId: string
  websiteUrl: string
  websiteName: string
  description?: string
  category: string
  monthlyTraffic: number
  region: string
  apiKey: string
  isActive: boolean
  balance: number
  createdAt: Date
  updatedAt: Date
}

export interface AdvertiserProfile {
  id: string
  userId: string
  companyName: string
  website?: string
  description?: string
  industry: string
  budget: number
  balance: number
  createdAt: Date
  updatedAt: Date
}

export interface Campaign {
  id: string
  advertiserId: string
  name: string
  description?: string
  budget: number
  dailyBudget?: number
  bidAmount: number
  pricingModel: PricingModel
  targetRegions: string[]
  targetCategories: string[]
  status: CampaignStatus
  startDate?: Date
  endDate?: Date
  createdAt: Date
  updatedAt: Date
  // Computed fields for dashboard display
  spent?: number
  impressions?: number
  clicks?: number
  conversions?: number
  ctr?: number
  costPerClick?: number
  costPerConversion?: number
  remainingBudget?: number
}

export interface AdSpace {
  id: string
  publisherId: string
  name: string
  format: AdFormat
  width: number
  height: number
  position: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Ad {
  id: string
  campaignId: string
  title: string
  description?: string
  imageUrl?: string
  videoUrl?: string
  clickUrl: string
  format: AdFormat
  width: number
  height: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  // Computed fields for dashboard display
  campaignName?: string
  impressions?: number
  clicks?: number
  conversions?: number
  ctr?: number
  costPerClick?: number
}

export interface AdPlacement {
  id: string
  campaignId: string
  adId: string
  adSpaceId: string
  isActive: boolean
  impressions: number
  clicks: number
  conversions: number
  revenue: number
  cost: number
  createdAt: Date
  updatedAt: Date
}

export interface DashboardStats {
  impressions: number
  clicks: number
  conversions: number
  revenue: number
  ctr: number
  conversionRate: number
}

// Advertiser-specific dashboard stats
export interface AdvertiserStats {
  totalSpent: number
  todaySpent: number
  totalImpressions: number
  todayImpressions: number
  totalClicks: number
  todayClicks: number
  ctr: number
  activeCampaigns: number
  remainingBudget: number
  conversions: number
  costPerConversion: number
}

// Analytics data interfaces
export interface AnalyticsData {
  date: string
  spent: number
  impressions: number
  clicks: number
  conversions: number
  ctr: number
  costPerClick: number
  costPerConversion: number
}

export interface CampaignPerformance {
  name: string
  spent: number
  impressions: number
  clicks: number
  conversions: number
  ctr: number
}

export interface RegionData {
  region: string
  spent: number
  conversions: number
  fill: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Form types for onboarding
export interface PublisherOnboardingData {
  websiteUrl: string
  websiteName: string
  description?: string
  category: string
  monthlyTraffic: string
  region: string
}

export interface AdvertiserOnboardingData {
  companyName: string
  website?: string
  description?: string
  industry: string
  budget: string
}

// Campaign creation data
export interface CampaignCreateData {
  name: string
  description?: string
  budget: number
  dailyBudget?: number
  pricingModel: PricingModel
  bidAmount: number
  targetRegions: string[]
  targetCategories: string[]
  startDate?: Date
  endDate?: Date
}

// Ad creation data
export interface AdCreateData {
  campaignId: string
  title: string
  description?: string
  imageUrl?: string
  videoUrl?: string
  clickUrl: string
  format: AdFormat
  width: number
  height: number
}

// Auth types
export interface SignUpData {
  name: string
  email: string
  password: string
  confirmPassword: string
  role: UserRole
}

export interface SignInData {
  email: string
  password: string
}

export { UserRole, CampaignStatus, AdFormat, PricingModel }
